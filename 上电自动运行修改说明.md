# 上电自动运行修改说明

## 修改目标
让系统上电后自动处于运行状态（一圈模式），电机立即开始转动，无需按键启动。

## 修改内容

### 1. 变量初始化修改
**文件**: `empty.c`
**位置**: 第20-21行

**修改前**:
```c
volatile unsigned int D_Num=0;
volatile int Run;
```

**修改后**:
```c
volatile unsigned int D_Num=1; // 初始化为1圈状态
volatile int Run=1; // 初始化为运行状态
```

### 2. OLED显示初始化
**文件**: `empty.c`
**位置**: 第61-62行

**修改前**:
```c
OLED_ShowString(0,16,(uint8_t*)"D:",12,1);
scheduler_init();
```

**修改后**:
```c
OLED_ShowString(0,16,(uint8_t*)"D:",12,1);
OLED_ShowSignedNum(12, 16, D_Num, 1, 12, 1); // 显示初始D_Num值
OLED_Refresh(); // 刷新显示
scheduler_init();
```

## 修改原理

### 运行控制逻辑
系统的运行控制基于以下变量：
- `Run`: 控制系统是否运行（0=停止，1=运行）
- `D_Num`: 控制运行圈数（0-5圈可选）

### 运行条件
在主循环中，系统运行需要满足：
```c
if(Run)
{
    if((D_Num*13252) > encoderB_cnt)
    {
        DL_GPIO_setPins(Stop_PORT, Stop_PIN_4_PIN);
        Way(Digtal); // 执行循迹控制
    }
}
```

### 编码器计数逻辑
- `D_Num * 13252`: 目标编码器计数值
- `encoderB_cnt`: 当前编码器B的计数值
- 当目标值大于当前值时，系统继续运行
- 13252是一圈对应的编码器脉冲数

## 修改效果

### 上电后行为
1. **立即运行**: 系统上电后`Run=1`，立即进入运行状态
2. **一圈模式**: `D_Num=1`，系统设置为运行一圈
3. **显示更新**: OLED立即显示当前圈数设置
4. **电机启动**: 满足运行条件后，电机根据灰度传感器信号开始循迹

### 按键功能保持
- **按键1**: 仍可用于切换圈数（0-5圈循环）
- **按键2**: 仍可用于启动/停止控制

## 注意事项

1. **安全提醒**: 上电后系统会立即运行，请确保小车放置在安全的循迹轨道上
2. **停止方法**: 可通过按键2停止运行，或断电重启
3. **圈数调整**: 按键1可在运行过程中调整目标圈数
4. **编码器复位**: 系统重启后编码器计数会重新开始

## 测试建议

1. **上电测试**: 确认系统上电后OLED显示"D:1"
2. **运行测试**: 确认电机开始转动并执行循迹
3. **按键测试**: 验证按键功能仍然正常
4. **停止测试**: 确认可以通过按键停止运行
