******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Sat Aug  2 11:18:05 2025

OUTPUT FILE NAME:   <test1.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00002a5d


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00004c98  0001b368  R  X
  SRAM                  20200000   00008000  00000923  000076dd  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00004c98   00004c98    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00003130   00003130    r-x .text
  000031f0    000031f0    00001a50   00001a50    r-- .rodata
  00004c40    00004c40    00000058   00000058    r-- .cinit
20200000    20200000    00000726   00000000    rw-
  20200000    20200000    0000055d   00000000    rw- .bss
  20200560    20200560    000001c6   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00003130     
                  000000c0    00000580     Ganway.o (.text.Way)
                  00000640    000001d0     oled.o (.text.OLED_ShowChar)
                  00000810    00000194     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  000009a4    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  00000b36    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00000b38    00000188     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init)
                  00000cc0    00000174     empty.o (.text.main)
                  00000e34    00000120     encoder.o (.text.GROUP1_IRQHandler)
                  00000f54    0000010c     motor.o (.text.Set_PWM)
                  00001060    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  0000116c    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00001270    000000e8                 : dl_timer.o (.text.DL_Timer_initTimerMode)
                  00001358    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  0000143c    000000e2     oled.o (.text.OLED_ShowNum)
                  0000151e    000000de     oled.o (.text.OLED_Init)
                  000015fc    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  000016d8    000000d8     empty.o (.text.TIMG0_IRQHandler)
                  000017b0    000000d0     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Analog_value)
                  00001880    000000aa     No_Mcu_Ganv_Grayscale_Sensor.o (.text.normalizeAnalogValues)
                  0000192a    0000009a     oled.o (.text.OLED_ShowSignedNum)
                  000019c4    0000009a     oled.o (.text.OLED_ShowString)
                  00001a5e    00000002     --HOLE-- [fill = 0]
                  00001a60    00000090     oled.o (.text.OLED_DrawPoint)
                  00001af0    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_0_init)
                  00001b7c    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00001c08    00000084     oled.o (.text.OLED_Refresh)
                  00001c8c    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00001d10    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00001d8c    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00001e00    00000072     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init_Frist)
                  00001e72    00000002     --HOLE-- [fill = 0]
                  00001e74    0000006c     oled.o (.text.OLED_WR_Byte)
                  00001ee0    0000006c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.convertAnalogToDigital)
                  00001f4c    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00001fb4    00000062                            : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00002016    00000002     --HOLE-- [fill = 0]
                  00002018    00000060     oled.o (.text.OLED_Clear)
                  00002078    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  000020d6    00000002     --HOLE-- [fill = 0]
                  000020d8    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00002130    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  00002184    00000050     oled.o (.text.DL_I2C_startControllerTransfer)
                  000021d4    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  00002224    0000004c     empty.o (.text.DL_ADC12_initSingleSample)
                  00002270    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  000022bc    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  00002306    00000002     --HOLE-- [fill = 0]
                  00002308    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00002352    0000004a     No_Mcu_Ganv_Grayscale_Sensor.o (.text.adc_getValue)
                  0000239c    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  000023e4    00000048     oled.o (.text.OLED_DisplayTurn)
                  0000242c    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC12_0_init)
                  00002474    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  000024bc    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  00002500    00000042     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_Mcu_Ganv_Sensor_Task_Without_tick)
                  00002542    00000002     --HOLE-- [fill = 0]
                  00002544    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  00002584    00000040     key.o (.text.Key)
                  000025c4    00000040     key.o (.text.Key_1)
                  00002604    00000040     bsp_usart.o (.text.UART0_IRQHandler)
                  00002644    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00002684    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  000026c0    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  000026fc    0000003c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Anolog_Value)
                  00002738    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00002774    0000003a     libclang_rt.builtins.a : muldsi3.S.obj (.text.__muldsi3)
                  000027ae    00000002     --HOLE-- [fill = 0]
                  000027b0    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  000027e4    00000034     oled.o (.text.OLED_ColorTurn)
                  00002818    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_0_init)
                  0000284c    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00002880    00000030     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_getMemResult)
                  000028b0    00000030     oled.o (.text.OLED_Pow)
                  000028e0    00000030     systick.o (.text.SysTick_Handler)
                  00002910    0000002c     empty.o (.text.__NVIC_ClearPendingIRQ)
                  0000293c    0000002c     empty.o (.text.__NVIC_EnableIRQ)
                  00002968    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00002994    00000028     empty.o (.text.DL_Common_updateReg)
                  000029bc    00000028     oled.o (.text.DL_Common_updateReg)
                  000029e4    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00002a0c    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00002a34    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00002a5c    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00002a84    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00002aaa    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00002ad0    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  00002af4    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00002b14    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00002b34    00000020     systick.o (.text.delay_ms)
                  00002b54    0000001e     ti_msp_dl_config.o (.text.DL_ADC12_setPowerDownMode)
                  00002b72    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00002b90    0000001c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_startConversion)
                  00002bac    0000001c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_stopConversion)
                  00002bc8    0000001c     encoder.o (.text.DL_GPIO_clearInterruptStatus)
                  00002be4    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00002c00    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  00002c1c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00002c38    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00002c54    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00002c70    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00002c8c    0000001c     ti_msp_dl_config.o (.text.DL_Timer_enableInterrupt)
                  00002ca8    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00002cc4    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00002ce0    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  00002cfc    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  00002d14    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  00002d2c    00000018     ti_msp_dl_config.o (.text.DL_ADC12_setSampleTime0)
                  00002d44    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00002d5c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00002d74    00000018     encoder.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  00002d8c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00002da4    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00002dbc    00000018     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_setPins)
                  00002dd4    00000018     empty.o (.text.DL_GPIO_setPins)
                  00002dec    00000018     motor.o (.text.DL_GPIO_setPins)
                  00002e04    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  00002e1c    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  00002e34    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  00002e4c    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00002e64    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00002e7c    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  00002e94    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00002eac    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00002ec4    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00002edc    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00002ef4    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00002f0c    00000018     empty.o (.text.DL_Timer_startCounter)
                  00002f24    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00002f3c    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00002f54    00000016     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_disableConversions)
                  00002f6a    00000016     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_enableConversions)
                  00002f80    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  00002f96    00000016     encoder.o (.text.DL_GPIO_readPins)
                  00002fac    00000016     key.o (.text.DL_GPIO_readPins)
                  00002fc2    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  00002fd8    00000014     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_clearPins)
                  00002fec    00000014     empty.o (.text.DL_GPIO_clearPins)
                  00003000    00000014     motor.o (.text.DL_GPIO_clearPins)
                  00003014    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00003028    00000014     oled.o (.text.DL_I2C_getControllerStatus)
                  0000303c    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  00003050    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00003064    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00003078    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  0000308c    00000014     bsp_usart.o (.text.DL_UART_receiveData)
                  000030a0    00000014     motor.o (.text.Left_Control)
                  000030b4    00000014     motor.o (.text.Right_Control)
                  000030c8    00000014     motor.o (.text.Right_Little_Control)
                  000030dc    00000012     empty.o (.text.DL_Timer_getPendingInterrupt)
                  000030ee    00000012     bsp_usart.o (.text.DL_UART_getPendingInterrupt)
                  00003100    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00003112    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00003124    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00003136    00000010     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_getStatus)
                  00003146    00000002     --HOLE-- [fill = 0]
                  00003148    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00003158    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00003168    00000010     libc.a : copy_zero_init.c.obj (.text:decompress:ZI)
                  00003178    0000000e     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Digtal_For_User)
                  00003186    0000000e     libc.a : memset16.S.obj (.text:TI_memset_small)
                  00003194    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  000031a0    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  000031ac    0000000c     systick.o (.text.get_systicks)
                  000031b8    0000000c     Scheduler.o (.text.scheduler_init)
                  000031c4    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  000031ce    00000002     --HOLE-- [fill = 0]
                  000031d0    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  000031d8    00000006     libc.a : exit.c.obj (.text:abort)
                  000031de    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  000031e2    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  000031e6    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  000031ea    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  000031ee    00000002     --HOLE-- [fill = 0]

.cinit     0    00004c40    00000058     
                  00004c40    00000033     (.cinit..data.load) [load image, compression = lzss]
                  00004c73    00000001     --HOLE-- [fill = 0]
                  00004c74    0000000c     (__TI_handler_table)
                  00004c80    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00004c88    00000010     (__TI_cinit_table)

.rodata    0    000031f0    00001a50     
                  000031f0    00000d5c     oled.o (.rodata.asc2_2412)
                  00003f4c    000005f0     oled.o (.rodata.asc2_1608)
                  0000453c    00000474     oled.o (.rodata.asc2_1206)
                  000049b0    00000228     oled.o (.rodata.asc2_0806)
                  00004bd8    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  00004c00    00000014     ti_msp_dl_config.o (.rodata.gTIMER_0TimerConfig)
                  00004c14    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  00004c1e    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00004c20    00000008     ti_msp_dl_config.o (.rodata.gADC12_0ClockConfig)
                  00004c28    00000008     ti_msp_dl_config.o (.rodata.gPWM_0Config)
                  00004c30    00000003     ti_msp_dl_config.o (.rodata.gPWM_0ClockConfig)
                  00004c33    00000003     ti_msp_dl_config.o (.rodata.gTIMER_0ClockConfig)
                  00004c36    00000003     empty.o (.rodata.str1.9517790425240694019.1)
                  00004c39    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  00004c3b    00000005     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    0000055d     UNINITIALIZED
                  20200000    00000480     (.common:OLED_GRAM)
                  20200480    000000bc     (.common:gPWM_0Backup)
                  2020053c    00000004     (.common:Flag_stop)
                  20200540    00000004     (.common:Flag_stop1)
                  20200544    00000004     (.common:Get_Encoder_countA)
                  20200548    00000004     (.common:Get_Encoder_countB)
                  2020054c    00000004     (.common:encoderA_cnt)
                  20200550    00000004     (.common:encoderB_cnt)
                  20200554    00000004     (.common:gpio_interrup1)
                  20200558    00000004     (.common:gpio_interrup2)
                  2020055c    00000001     (.common:task_num)

.data      0    20200560    000001c6     UNINITIALIZED
                  20200560    00000100     empty.o (.data.rx_buff)
                  20200660    00000080     bsp_usart.o (.data.uart_rx_buffer)
                  202006e0    00000010     empty.o (.data.Anolog)
                  202006f0    00000010     empty.o (.data.black)
                  20200700    00000010     empty.o (.data.white)
                  20200710    00000008     systick.o (.data.systicks)
                  20200718    00000004     empty.o (.data.D_Num)
                  2020071c    00000004     empty.o (.data.Run)
                  20200720    00000004     systick.o (.data.delay_times)
                  20200724    00000001     bsp_usart.o (.data.uart_rx_index)
                  20200725    00000001     bsp_usart.o (.data.uart_rx_ticks)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                           code    ro data   rw data
       ------                           ----    -------   -------
    .\
       ti_msp_dl_config.o               2772    96        188    
       empty.o                          878     3         328    
       startup_mspm0g350x_ticlang.o     8       192       0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           3658    291       516    
                                                                 
    .\app\
       No_Mcu_Ganv_Grayscale_Sensor.o   1414    0         0      
       Ganway.o                         1408    0         0      
       encoder.o                        362     0         16     
       motor.o                          372     0         0      
       key.o                            150     0         0      
       Scheduler.o                      12      0         1      
    +--+--------------------------------+-------+---------+---------+
       Total:                           3718    0         17     
                                                                 
    .\app\OLED\
       oled.o                           2012    6632      1152   
    +--+--------------------------------+-------+---------+---------+
       Total:                           2012    6632      1152   
                                                                 
    .\bsp\
       bsp_usart.o                      102     0         130    
       systick.o                        92      0         12     
    +--+--------------------------------+-------+---------+---------+
       Total:                           194     0         142    
                                                                 
    C:/ti/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                       588     0         0      
       dl_sysctl_mspm0g1x0x_g3x0x.o     288     0         0      
       dl_i2c.o                         132     0         0      
       dl_uart.o                        90      0         0      
       dl_adc12.o                       64      0         0      
       dl_common.o                      10      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           1172    0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       copy_decompress_lzss.c.obj       124     0         0      
       autoinit.c.obj                   60      0         0      
       boot_cortex_m.c.obj              40      0         0      
       copy_decompress_none.c.obj       18      0         0      
       memcpy16.S.obj                   18      0         0      
       copy_zero_init.c.obj             16      0         0      
       memset16.S.obj                   14      0         0      
       exit.c.obj                       6       0         0      
       pre_init.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           300     0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           4       0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                     402     0         0      
       divdf3.S.obj                     268     0         0      
       muldf3.S.obj                     228     0         0      
       comparedf2.c.obj                 220     0         0      
       aeabi_dcmp.S.obj                 98      0         0      
       fixdfsi.S.obj                    74      0         0      
       aeabi_uidivmod.S.obj             64      0         0      
       muldsi3.S.obj                    58      0         0      
       floatsidf.S.obj                  44      0         0      
       floatunsidf.S.obj                36      0         0      
       aeabi_memset.S.obj               12      0         0      
       aeabi_memcpy.S.obj               8       0         0      
       aeabi_div0.c.obj                 2       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           1514    0         0      
                                                                 
       Stack:                           0       0         512    
       Linker Generated:                0       87        0      
    +--+--------------------------------+-------+---------+---------+
       Grand Total:                     12572   7010      2339   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00004c88 records: 2, size/record: 8, table size: 16
	.data: load addr=00004c40, load size=00000033 bytes, run addr=20200560, run size=000001c6 bytes, compression=lzss
	.bss: load addr=00004c80, load size=00000008 bytes, run addr=20200000, run size=0000055d bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00004c74 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
000031df  ADC0_IRQHandler                      
000031df  ADC1_IRQHandler                      
000031df  AES_IRQHandler                       
202006e0  Anolog                               
000031e2  C$$EXIT                              
000031df  CANFD0_IRQHandler                    
000031df  DAC0_IRQHandler                      
00002545  DL_ADC12_setClockConfig              
000031c5  DL_Common_delayCycles                
00002079  DL_I2C_fillControllerTXFIFO          
00002aab  DL_I2C_setClockConfig                
000015fd  DL_SYSCTL_configSYSPLL               
000024bd  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
0000116d  DL_Timer_initFourCCPWMMode           
00001271  DL_Timer_initTimerMode               
00002ca9  DL_Timer_setCaptCompUpdateMethod     
00002ef5  DL_Timer_setCaptureCompareOutCtl     
00003159  DL_Timer_setCaptureCompareValue      
00002cc5  DL_Timer_setClockConfig              
0000239d  DL_UART_init                         
00003101  DL_UART_setClockConfig               
000031df  DMA_IRQHandler                       
20200718  D_Num                                
000031df  Default_Handler                      
2020053c  Flag_stop                            
20200540  Flag_stop1                           
000031df  GROUP0_IRQHandler                    
00000e35  GROUP1_IRQHandler                    
000017b1  Get_Analog_value                     
000026fd  Get_Anolog_Value                     
00003179  Get_Digtal_For_User                  
20200544  Get_Encoder_countA                   
20200548  Get_Encoder_countB                   
000031e3  HOSTexit                             
000031df  HardFault_Handler                    
000031df  I2C0_IRQHandler                      
000031df  I2C1_IRQHandler                      
00002585  Key                                  
000025c5  Key_1                                
000030a1  Left_Control                         
000031df  NMI_Handler                          
00000b39  No_MCU_Ganv_Sensor_Init              
00001e01  No_MCU_Ganv_Sensor_Init_Frist        
00002501  No_Mcu_Ganv_Sensor_Task_Without_tick 
00002019  OLED_Clear                           
000027e5  OLED_ColorTurn                       
000023e5  OLED_DisplayTurn                     
00001a61  OLED_DrawPoint                       
20200000  OLED_GRAM                            
0000151f  OLED_Init                            
000028b1  OLED_Pow                             
00001c09  OLED_Refresh                         
00000641  OLED_ShowChar                        
0000143d  OLED_ShowNum                         
0000192b  OLED_ShowSignedNum                   
000019c5  OLED_ShowString                      
00001e75  OLED_WR_Byte                         
000031df  PendSV_Handler                       
000031df  RTC_IRQHandler                       
000031e7  Reset_Handler                        
000030b5  Right_Control                        
000030c9  Right_Little_Control                 
2020071c  Run                                  
000031df  SPI0_IRQHandler                      
000031df  SPI1_IRQHandler                      
000031df  SVC_Handler                          
0000242d  SYSCFG_DL_ADC12_0_init               
00000811  SYSCFG_DL_GPIO_init                  
000020d9  SYSCFG_DL_I2C_OLED_init              
00001af1  SYSCFG_DL_PWM_0_init                 
00002475  SYSCFG_DL_SYSCTL_init                
00003195  SYSCFG_DL_SYSTICK_init               
00002819  SYSCFG_DL_TIMER_0_init               
00002131  SYSCFG_DL_UART_0_init                
0000284d  SYSCFG_DL_init                       
00001b7d  SYSCFG_DL_initPower                  
00000f55  Set_PWM                              
000028e1  SysTick_Handler                      
000031df  TIMA0_IRQHandler                     
000031df  TIMA1_IRQHandler                     
000016d9  TIMG0_IRQHandler                     
000031df  TIMG12_IRQHandler                    
000031df  TIMG6_IRQHandler                     
000031df  TIMG7_IRQHandler                     
000031df  TIMG8_IRQHandler                     
00003113  TI_memcpy_small                      
00003187  TI_memset_small                      
00002605  UART0_IRQHandler                     
000031df  UART1_IRQHandler                     
000031df  UART2_IRQHandler                     
000031df  UART3_IRQHandler                     
000000c1  Way                                  
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00004c88  __TI_CINIT_Base                      
00004c98  __TI_CINIT_Limit                     
00004c98  __TI_CINIT_Warm                      
00004c74  __TI_Handler_Table_Base              
00004c80  __TI_Handler_Table_Limit             
00002739  __TI_auto_init_nobinit_nopinit       
00001d11  __TI_decompress_lzss                 
00003125  __TI_decompress_none                 
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
00003169  __TI_zero_init                       
000009af  __adddf3                             
00002309  __aeabi_d2iz                         
000009af  __aeabi_dadd                         
00001fb5  __aeabi_dcmpeq                       
00001ff1  __aeabi_dcmpge                       
00002005  __aeabi_dcmpgt                       
00001fdd  __aeabi_dcmple                       
00001fc9  __aeabi_dcmplt                       
00001061  __aeabi_ddiv                         
00001359  __aeabi_dmul                         
000009a5  __aeabi_dsub                         
00002969  __aeabi_i2d                          
00000b37  __aeabi_idiv0                        
000031a1  __aeabi_memclr                       
000031a1  __aeabi_memclr4                      
000031a1  __aeabi_memclr8                      
000031d1  __aeabi_memcpy                       
000031d1  __aeabi_memcpy4                      
000031d1  __aeabi_memcpy8                      
00002ad1  __aeabi_ui2d                         
00002645  __aeabi_uidiv                        
00002645  __aeabi_uidivmod                     
ffffffff  __binit__                            
00001f4d  __cmpdf2                             
00001061  __divdf3                             
00001f4d  __eqdf2                              
00002309  __fixdfsi                            
00002969  __floatsidf                          
00002ad1  __floatunsidf                        
00001d8d  __gedf2                              
00001d8d  __gtdf2                              
00001f4d  __ledf2                              
00001f4d  __ltdf2                              
UNDEFED   __mpu_init                           
00001359  __muldf3                             
00002775  __muldsi3                            
00001f4d  __nedf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
000009a5  __subdf3                             
00002a5d  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
000031eb  _system_pre_init                     
000031d9  abort                                
00002353  adc_getValue                         
000049b0  asc2_0806                            
0000453c  asc2_1206                            
00003f4c  asc2_1608                            
000031f0  asc2_2412                            
ffffffff  binit                                
202006f0  black                                
00001ee1  convertAnalogToDigital               
00002b35  delay_ms                             
20200720  delay_times                          
2020054c  encoderA_cnt                         
20200550  encoderB_cnt                         
20200480  gPWM_0Backup                         
000031ad  get_systicks                         
20200554  gpio_interrup1                       
20200558  gpio_interrup2                       
00000000  interruptVectors                     
00000cc1  main                                 
00001881  normalizeAnalogValues                
20200560  rx_buff                              
000031b9  scheduler_init                       
2020055c  task_num                             
20200660  uart_rx_buffer                       
20200724  uart_rx_index                        
20200725  uart_rx_ticks                        
20200700  white                                


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  Way                                  
00000200  __STACK_SIZE                         
00000641  OLED_ShowChar                        
00000811  SYSCFG_DL_GPIO_init                  
000009a5  __aeabi_dsub                         
000009a5  __subdf3                             
000009af  __adddf3                             
000009af  __aeabi_dadd                         
00000b37  __aeabi_idiv0                        
00000b39  No_MCU_Ganv_Sensor_Init              
00000cc1  main                                 
00000e35  GROUP1_IRQHandler                    
00000f55  Set_PWM                              
00001061  __aeabi_ddiv                         
00001061  __divdf3                             
0000116d  DL_Timer_initFourCCPWMMode           
00001271  DL_Timer_initTimerMode               
00001359  __aeabi_dmul                         
00001359  __muldf3                             
0000143d  OLED_ShowNum                         
0000151f  OLED_Init                            
000015fd  DL_SYSCTL_configSYSPLL               
000016d9  TIMG0_IRQHandler                     
000017b1  Get_Analog_value                     
00001881  normalizeAnalogValues                
0000192b  OLED_ShowSignedNum                   
000019c5  OLED_ShowString                      
00001a61  OLED_DrawPoint                       
00001af1  SYSCFG_DL_PWM_0_init                 
00001b7d  SYSCFG_DL_initPower                  
00001c09  OLED_Refresh                         
00001d11  __TI_decompress_lzss                 
00001d8d  __gedf2                              
00001d8d  __gtdf2                              
00001e01  No_MCU_Ganv_Sensor_Init_Frist        
00001e75  OLED_WR_Byte                         
00001ee1  convertAnalogToDigital               
00001f4d  __cmpdf2                             
00001f4d  __eqdf2                              
00001f4d  __ledf2                              
00001f4d  __ltdf2                              
00001f4d  __nedf2                              
00001fb5  __aeabi_dcmpeq                       
00001fc9  __aeabi_dcmplt                       
00001fdd  __aeabi_dcmple                       
00001ff1  __aeabi_dcmpge                       
00002005  __aeabi_dcmpgt                       
00002019  OLED_Clear                           
00002079  DL_I2C_fillControllerTXFIFO          
000020d9  SYSCFG_DL_I2C_OLED_init              
00002131  SYSCFG_DL_UART_0_init                
00002309  __aeabi_d2iz                         
00002309  __fixdfsi                            
00002353  adc_getValue                         
0000239d  DL_UART_init                         
000023e5  OLED_DisplayTurn                     
0000242d  SYSCFG_DL_ADC12_0_init               
00002475  SYSCFG_DL_SYSCTL_init                
000024bd  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00002501  No_Mcu_Ganv_Sensor_Task_Without_tick 
00002545  DL_ADC12_setClockConfig              
00002585  Key                                  
000025c5  Key_1                                
00002605  UART0_IRQHandler                     
00002645  __aeabi_uidiv                        
00002645  __aeabi_uidivmod                     
000026fd  Get_Anolog_Value                     
00002739  __TI_auto_init_nobinit_nopinit       
00002775  __muldsi3                            
000027e5  OLED_ColorTurn                       
00002819  SYSCFG_DL_TIMER_0_init               
0000284d  SYSCFG_DL_init                       
000028b1  OLED_Pow                             
000028e1  SysTick_Handler                      
00002969  __aeabi_i2d                          
00002969  __floatsidf                          
00002a5d  _c_int00_noargs                      
00002aab  DL_I2C_setClockConfig                
00002ad1  __aeabi_ui2d                         
00002ad1  __floatunsidf                        
00002b35  delay_ms                             
00002ca9  DL_Timer_setCaptCompUpdateMethod     
00002cc5  DL_Timer_setClockConfig              
00002ef5  DL_Timer_setCaptureCompareOutCtl     
000030a1  Left_Control                         
000030b5  Right_Control                        
000030c9  Right_Little_Control                 
00003101  DL_UART_setClockConfig               
00003113  TI_memcpy_small                      
00003125  __TI_decompress_none                 
00003159  DL_Timer_setCaptureCompareValue      
00003169  __TI_zero_init                       
00003179  Get_Digtal_For_User                  
00003187  TI_memset_small                      
00003195  SYSCFG_DL_SYSTICK_init               
000031a1  __aeabi_memclr                       
000031a1  __aeabi_memclr4                      
000031a1  __aeabi_memclr8                      
000031ad  get_systicks                         
000031b9  scheduler_init                       
000031c5  DL_Common_delayCycles                
000031d1  __aeabi_memcpy                       
000031d1  __aeabi_memcpy4                      
000031d1  __aeabi_memcpy8                      
000031d9  abort                                
000031df  ADC0_IRQHandler                      
000031df  ADC1_IRQHandler                      
000031df  AES_IRQHandler                       
000031df  CANFD0_IRQHandler                    
000031df  DAC0_IRQHandler                      
000031df  DMA_IRQHandler                       
000031df  Default_Handler                      
000031df  GROUP0_IRQHandler                    
000031df  HardFault_Handler                    
000031df  I2C0_IRQHandler                      
000031df  I2C1_IRQHandler                      
000031df  NMI_Handler                          
000031df  PendSV_Handler                       
000031df  RTC_IRQHandler                       
000031df  SPI0_IRQHandler                      
000031df  SPI1_IRQHandler                      
000031df  SVC_Handler                          
000031df  TIMA0_IRQHandler                     
000031df  TIMA1_IRQHandler                     
000031df  TIMG12_IRQHandler                    
000031df  TIMG6_IRQHandler                     
000031df  TIMG7_IRQHandler                     
000031df  TIMG8_IRQHandler                     
000031df  UART1_IRQHandler                     
000031df  UART2_IRQHandler                     
000031df  UART3_IRQHandler                     
000031e2  C$$EXIT                              
000031e3  HOSTexit                             
000031e7  Reset_Handler                        
000031eb  _system_pre_init                     
000031f0  asc2_2412                            
00003f4c  asc2_1608                            
0000453c  asc2_1206                            
000049b0  asc2_0806                            
00004c74  __TI_Handler_Table_Base              
00004c80  __TI_Handler_Table_Limit             
00004c88  __TI_CINIT_Base                      
00004c98  __TI_CINIT_Limit                     
00004c98  __TI_CINIT_Warm                      
20200000  OLED_GRAM                            
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
20200480  gPWM_0Backup                         
2020053c  Flag_stop                            
20200540  Flag_stop1                           
20200544  Get_Encoder_countA                   
20200548  Get_Encoder_countB                   
2020054c  encoderA_cnt                         
20200550  encoderB_cnt                         
20200554  gpio_interrup1                       
20200558  gpio_interrup2                       
2020055c  task_num                             
20200560  rx_buff                              
20200660  uart_rx_buffer                       
202006e0  Anolog                               
202006f0  black                                
20200700  white                                
20200718  D_Num                                
2020071c  Run                                  
20200720  delay_times                          
20200724  uart_rx_index                        
20200725  uart_rx_ticks                        
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[189 symbols]
