******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Sat Aug  2 11:11:34 2025

OUTPUT FILE NAME:   <test1.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00002a19


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00004c58  0001b3a8  R  X
  SRAM                  20200000   00008000  00000923  000076dd  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00004c58   00004c58    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    000030f0   000030f0    r-x .text
  000031b0    000031b0    00001a50   00001a50    r-- .rodata
  00004c00    00004c00    00000058   00000058    r-- .cinit
20200000    20200000    00000726   00000000    rw-
  20200000    20200000    0000055d   00000000    rw- .bss
  20200560    20200560    000001c6   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    000030f0     
                  000000c0    00000580     Ganway.o (.text.Way)
                  00000640    000001d0     oled.o (.text.OLED_ShowChar)
                  00000810    00000194     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  000009a4    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  00000b36    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00000b38    00000188     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init)
                  00000cc0    00000130     empty.o (.text.main)
                  00000df0    00000120     encoder.o (.text.GROUP1_IRQHandler)
                  00000f10    0000010c     motor.o (.text.Set_PWM)
                  0000101c    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00001128    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  0000122c    000000e8                 : dl_timer.o (.text.DL_Timer_initTimerMode)
                  00001314    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  000013f8    000000e2     oled.o (.text.OLED_ShowNum)
                  000014da    000000de     oled.o (.text.OLED_Init)
                  000015b8    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  00001694    000000d8     empty.o (.text.TIMG0_IRQHandler)
                  0000176c    000000d0     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Analog_value)
                  0000183c    000000aa     No_Mcu_Ganv_Grayscale_Sensor.o (.text.normalizeAnalogValues)
                  000018e6    0000009a     oled.o (.text.OLED_ShowSignedNum)
                  00001980    0000009a     oled.o (.text.OLED_ShowString)
                  00001a1a    00000002     --HOLE-- [fill = 0]
                  00001a1c    00000090     oled.o (.text.OLED_DrawPoint)
                  00001aac    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_0_init)
                  00001b38    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00001bc4    00000084     oled.o (.text.OLED_Refresh)
                  00001c48    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00001ccc    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00001d48    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00001dbc    00000072     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init_Frist)
                  00001e2e    00000002     --HOLE-- [fill = 0]
                  00001e30    0000006c     oled.o (.text.OLED_WR_Byte)
                  00001e9c    0000006c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.convertAnalogToDigital)
                  00001f08    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00001f70    00000062                            : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00001fd2    00000002     --HOLE-- [fill = 0]
                  00001fd4    00000060     oled.o (.text.OLED_Clear)
                  00002034    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  00002092    00000002     --HOLE-- [fill = 0]
                  00002094    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  000020ec    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  00002140    00000050     oled.o (.text.DL_I2C_startControllerTransfer)
                  00002190    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  000021e0    0000004c     empty.o (.text.DL_ADC12_initSingleSample)
                  0000222c    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  00002278    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  000022c2    00000002     --HOLE-- [fill = 0]
                  000022c4    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  0000230e    0000004a     No_Mcu_Ganv_Grayscale_Sensor.o (.text.adc_getValue)
                  00002358    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  000023a0    00000048     oled.o (.text.OLED_DisplayTurn)
                  000023e8    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC12_0_init)
                  00002430    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00002478    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  000024bc    00000042     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_Mcu_Ganv_Sensor_Task_Without_tick)
                  000024fe    00000002     --HOLE-- [fill = 0]
                  00002500    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  00002540    00000040     key.o (.text.Key)
                  00002580    00000040     key.o (.text.Key_1)
                  000025c0    00000040     bsp_usart.o (.text.UART0_IRQHandler)
                  00002600    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00002640    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  0000267c    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  000026b8    0000003c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Anolog_Value)
                  000026f4    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00002730    0000003a     libclang_rt.builtins.a : muldsi3.S.obj (.text.__muldsi3)
                  0000276a    00000002     --HOLE-- [fill = 0]
                  0000276c    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  000027a0    00000034     oled.o (.text.OLED_ColorTurn)
                  000027d4    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_0_init)
                  00002808    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  0000283c    00000030     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_getMemResult)
                  0000286c    00000030     oled.o (.text.OLED_Pow)
                  0000289c    00000030     systick.o (.text.SysTick_Handler)
                  000028cc    0000002c     empty.o (.text.__NVIC_ClearPendingIRQ)
                  000028f8    0000002c     empty.o (.text.__NVIC_EnableIRQ)
                  00002924    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00002950    00000028     empty.o (.text.DL_Common_updateReg)
                  00002978    00000028     oled.o (.text.DL_Common_updateReg)
                  000029a0    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  000029c8    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  000029f0    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00002a18    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00002a40    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00002a66    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00002a8c    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  00002ab0    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00002ad0    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00002af0    00000020     systick.o (.text.delay_ms)
                  00002b10    0000001e     ti_msp_dl_config.o (.text.DL_ADC12_setPowerDownMode)
                  00002b2e    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00002b4c    0000001c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_startConversion)
                  00002b68    0000001c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_stopConversion)
                  00002b84    0000001c     encoder.o (.text.DL_GPIO_clearInterruptStatus)
                  00002ba0    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00002bbc    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  00002bd8    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00002bf4    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00002c10    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00002c2c    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00002c48    0000001c     ti_msp_dl_config.o (.text.DL_Timer_enableInterrupt)
                  00002c64    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00002c80    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00002c9c    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  00002cb8    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  00002cd0    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  00002ce8    00000018     ti_msp_dl_config.o (.text.DL_ADC12_setSampleTime0)
                  00002d00    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00002d18    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00002d30    00000018     encoder.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  00002d48    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00002d60    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00002d78    00000018     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_setPins)
                  00002d90    00000018     empty.o (.text.DL_GPIO_setPins)
                  00002da8    00000018     motor.o (.text.DL_GPIO_setPins)
                  00002dc0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  00002dd8    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  00002df0    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  00002e08    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00002e20    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00002e38    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  00002e50    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00002e68    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00002e80    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00002e98    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00002eb0    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00002ec8    00000018     empty.o (.text.DL_Timer_startCounter)
                  00002ee0    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00002ef8    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00002f10    00000016     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_disableConversions)
                  00002f26    00000016     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_enableConversions)
                  00002f3c    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  00002f52    00000016     encoder.o (.text.DL_GPIO_readPins)
                  00002f68    00000016     key.o (.text.DL_GPIO_readPins)
                  00002f7e    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  00002f94    00000014     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_clearPins)
                  00002fa8    00000014     empty.o (.text.DL_GPIO_clearPins)
                  00002fbc    00000014     motor.o (.text.DL_GPIO_clearPins)
                  00002fd0    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00002fe4    00000014     oled.o (.text.DL_I2C_getControllerStatus)
                  00002ff8    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  0000300c    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00003020    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00003034    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00003048    00000014     bsp_usart.o (.text.DL_UART_receiveData)
                  0000305c    00000014     motor.o (.text.Left_Control)
                  00003070    00000014     motor.o (.text.Right_Control)
                  00003084    00000014     motor.o (.text.Right_Little_Control)
                  00003098    00000012     empty.o (.text.DL_Timer_getPendingInterrupt)
                  000030aa    00000012     bsp_usart.o (.text.DL_UART_getPendingInterrupt)
                  000030bc    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  000030ce    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  000030e0    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  000030f2    00000010     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_getStatus)
                  00003102    00000002     --HOLE-- [fill = 0]
                  00003104    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00003114    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00003124    00000010     libc.a : copy_zero_init.c.obj (.text:decompress:ZI)
                  00003134    0000000e     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Digtal_For_User)
                  00003142    0000000e     libc.a : memset16.S.obj (.text:TI_memset_small)
                  00003150    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  0000315c    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  00003168    0000000c     systick.o (.text.get_systicks)
                  00003174    0000000c     Scheduler.o (.text.scheduler_init)
                  00003180    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  0000318a    00000002     --HOLE-- [fill = 0]
                  0000318c    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00003194    00000006     libc.a : exit.c.obj (.text:abort)
                  0000319a    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  0000319e    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  000031a2    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  000031a6    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  000031aa    00000006     --HOLE-- [fill = 0]

.cinit     0    00004c00    00000058     
                  00004c00    00000033     (.cinit..data.load) [load image, compression = lzss]
                  00004c33    00000001     --HOLE-- [fill = 0]
                  00004c34    0000000c     (__TI_handler_table)
                  00004c40    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00004c48    00000010     (__TI_cinit_table)

.rodata    0    000031b0    00001a50     
                  000031b0    00000d5c     oled.o (.rodata.asc2_2412)
                  00003f0c    000005f0     oled.o (.rodata.asc2_1608)
                  000044fc    00000474     oled.o (.rodata.asc2_1206)
                  00004970    00000228     oled.o (.rodata.asc2_0806)
                  00004b98    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  00004bc0    00000014     ti_msp_dl_config.o (.rodata.gTIMER_0TimerConfig)
                  00004bd4    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  00004bde    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00004be0    00000008     ti_msp_dl_config.o (.rodata.gADC12_0ClockConfig)
                  00004be8    00000008     ti_msp_dl_config.o (.rodata.gPWM_0Config)
                  00004bf0    00000003     ti_msp_dl_config.o (.rodata.gPWM_0ClockConfig)
                  00004bf3    00000003     ti_msp_dl_config.o (.rodata.gTIMER_0ClockConfig)
                  00004bf6    00000003     empty.o (.rodata.str1.9517790425240694019.1)
                  00004bf9    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  00004bfb    00000005     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    0000055d     UNINITIALIZED
                  20200000    00000480     (.common:OLED_GRAM)
                  20200480    000000bc     (.common:gPWM_0Backup)
                  2020053c    00000004     (.common:Flag_stop)
                  20200540    00000004     (.common:Flag_stop1)
                  20200544    00000004     (.common:Get_Encoder_countA)
                  20200548    00000004     (.common:Get_Encoder_countB)
                  2020054c    00000004     (.common:encoderA_cnt)
                  20200550    00000004     (.common:encoderB_cnt)
                  20200554    00000004     (.common:gpio_interrup1)
                  20200558    00000004     (.common:gpio_interrup2)
                  2020055c    00000001     (.common:task_num)

.data      0    20200560    000001c6     UNINITIALIZED
                  20200560    00000100     empty.o (.data.rx_buff)
                  20200660    00000080     bsp_usart.o (.data.uart_rx_buffer)
                  202006e0    00000010     empty.o (.data.Anolog)
                  202006f0    00000010     empty.o (.data.black)
                  20200700    00000010     empty.o (.data.white)
                  20200710    00000008     systick.o (.data.systicks)
                  20200718    00000004     empty.o (.data.D_Num)
                  2020071c    00000004     empty.o (.data.Run)
                  20200720    00000004     systick.o (.data.delay_times)
                  20200724    00000001     bsp_usart.o (.data.uart_rx_index)
                  20200725    00000001     bsp_usart.o (.data.uart_rx_ticks)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                           code    ro data   rw data
       ------                           ----    -------   -------
    .\
       ti_msp_dl_config.o               2772    96        188    
       empty.o                          810     3         328    
       startup_mspm0g350x_ticlang.o     8       192       0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           3590    291       516    
                                                                 
    .\app\
       No_Mcu_Ganv_Grayscale_Sensor.o   1414    0         0      
       Ganway.o                         1408    0         0      
       encoder.o                        362     0         16     
       motor.o                          372     0         0      
       key.o                            150     0         0      
       Scheduler.o                      12      0         1      
    +--+--------------------------------+-------+---------+---------+
       Total:                           3718    0         17     
                                                                 
    .\app\OLED\
       oled.o                           2012    6632      1152   
    +--+--------------------------------+-------+---------+---------+
       Total:                           2012    6632      1152   
                                                                 
    .\bsp\
       bsp_usart.o                      102     0         130    
       systick.o                        92      0         12     
    +--+--------------------------------+-------+---------+---------+
       Total:                           194     0         142    
                                                                 
    C:/ti/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                       588     0         0      
       dl_sysctl_mspm0g1x0x_g3x0x.o     288     0         0      
       dl_i2c.o                         132     0         0      
       dl_uart.o                        90      0         0      
       dl_adc12.o                       64      0         0      
       dl_common.o                      10      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           1172    0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       copy_decompress_lzss.c.obj       124     0         0      
       autoinit.c.obj                   60      0         0      
       boot_cortex_m.c.obj              40      0         0      
       copy_decompress_none.c.obj       18      0         0      
       memcpy16.S.obj                   18      0         0      
       copy_zero_init.c.obj             16      0         0      
       memset16.S.obj                   14      0         0      
       exit.c.obj                       6       0         0      
       pre_init.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           300     0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           4       0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                     402     0         0      
       divdf3.S.obj                     268     0         0      
       muldf3.S.obj                     228     0         0      
       comparedf2.c.obj                 220     0         0      
       aeabi_dcmp.S.obj                 98      0         0      
       fixdfsi.S.obj                    74      0         0      
       aeabi_uidivmod.S.obj             64      0         0      
       muldsi3.S.obj                    58      0         0      
       floatsidf.S.obj                  44      0         0      
       floatunsidf.S.obj                36      0         0      
       aeabi_memset.S.obj               12      0         0      
       aeabi_memcpy.S.obj               8       0         0      
       aeabi_div0.c.obj                 2       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           1514    0         0      
                                                                 
       Stack:                           0       0         512    
       Linker Generated:                0       87        0      
    +--+--------------------------------+-------+---------+---------+
       Grand Total:                     12504   7010      2339   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00004c48 records: 2, size/record: 8, table size: 16
	.data: load addr=00004c00, load size=00000033 bytes, run addr=20200560, run size=000001c6 bytes, compression=lzss
	.bss: load addr=00004c40, load size=00000008 bytes, run addr=20200000, run size=0000055d bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00004c34 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
0000319b  ADC0_IRQHandler                      
0000319b  ADC1_IRQHandler                      
0000319b  AES_IRQHandler                       
202006e0  Anolog                               
0000319e  C$$EXIT                              
0000319b  CANFD0_IRQHandler                    
0000319b  DAC0_IRQHandler                      
00002501  DL_ADC12_setClockConfig              
00003181  DL_Common_delayCycles                
00002035  DL_I2C_fillControllerTXFIFO          
00002a67  DL_I2C_setClockConfig                
000015b9  DL_SYSCTL_configSYSPLL               
00002479  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00001129  DL_Timer_initFourCCPWMMode           
0000122d  DL_Timer_initTimerMode               
00002c65  DL_Timer_setCaptCompUpdateMethod     
00002eb1  DL_Timer_setCaptureCompareOutCtl     
00003115  DL_Timer_setCaptureCompareValue      
00002c81  DL_Timer_setClockConfig              
00002359  DL_UART_init                         
000030bd  DL_UART_setClockConfig               
0000319b  DMA_IRQHandler                       
20200718  D_Num                                
0000319b  Default_Handler                      
2020053c  Flag_stop                            
20200540  Flag_stop1                           
0000319b  GROUP0_IRQHandler                    
00000df1  GROUP1_IRQHandler                    
0000176d  Get_Analog_value                     
000026b9  Get_Anolog_Value                     
00003135  Get_Digtal_For_User                  
20200544  Get_Encoder_countA                   
20200548  Get_Encoder_countB                   
0000319f  HOSTexit                             
0000319b  HardFault_Handler                    
0000319b  I2C0_IRQHandler                      
0000319b  I2C1_IRQHandler                      
00002541  Key                                  
00002581  Key_1                                
0000305d  Left_Control                         
0000319b  NMI_Handler                          
00000b39  No_MCU_Ganv_Sensor_Init              
00001dbd  No_MCU_Ganv_Sensor_Init_Frist        
000024bd  No_Mcu_Ganv_Sensor_Task_Without_tick 
00001fd5  OLED_Clear                           
000027a1  OLED_ColorTurn                       
000023a1  OLED_DisplayTurn                     
00001a1d  OLED_DrawPoint                       
20200000  OLED_GRAM                            
000014db  OLED_Init                            
0000286d  OLED_Pow                             
00001bc5  OLED_Refresh                         
00000641  OLED_ShowChar                        
000013f9  OLED_ShowNum                         
000018e7  OLED_ShowSignedNum                   
00001981  OLED_ShowString                      
00001e31  OLED_WR_Byte                         
0000319b  PendSV_Handler                       
0000319b  RTC_IRQHandler                       
000031a3  Reset_Handler                        
00003071  Right_Control                        
00003085  Right_Little_Control                 
2020071c  Run                                  
0000319b  SPI0_IRQHandler                      
0000319b  SPI1_IRQHandler                      
0000319b  SVC_Handler                          
000023e9  SYSCFG_DL_ADC12_0_init               
00000811  SYSCFG_DL_GPIO_init                  
00002095  SYSCFG_DL_I2C_OLED_init              
00001aad  SYSCFG_DL_PWM_0_init                 
00002431  SYSCFG_DL_SYSCTL_init                
00003151  SYSCFG_DL_SYSTICK_init               
000027d5  SYSCFG_DL_TIMER_0_init               
000020ed  SYSCFG_DL_UART_0_init                
00002809  SYSCFG_DL_init                       
00001b39  SYSCFG_DL_initPower                  
00000f11  Set_PWM                              
0000289d  SysTick_Handler                      
0000319b  TIMA0_IRQHandler                     
0000319b  TIMA1_IRQHandler                     
00001695  TIMG0_IRQHandler                     
0000319b  TIMG12_IRQHandler                    
0000319b  TIMG6_IRQHandler                     
0000319b  TIMG7_IRQHandler                     
0000319b  TIMG8_IRQHandler                     
000030cf  TI_memcpy_small                      
00003143  TI_memset_small                      
000025c1  UART0_IRQHandler                     
0000319b  UART1_IRQHandler                     
0000319b  UART2_IRQHandler                     
0000319b  UART3_IRQHandler                     
000000c1  Way                                  
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00004c48  __TI_CINIT_Base                      
00004c58  __TI_CINIT_Limit                     
00004c58  __TI_CINIT_Warm                      
00004c34  __TI_Handler_Table_Base              
00004c40  __TI_Handler_Table_Limit             
000026f5  __TI_auto_init_nobinit_nopinit       
00001ccd  __TI_decompress_lzss                 
000030e1  __TI_decompress_none                 
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
00003125  __TI_zero_init                       
000009af  __adddf3                             
000022c5  __aeabi_d2iz                         
000009af  __aeabi_dadd                         
00001f71  __aeabi_dcmpeq                       
00001fad  __aeabi_dcmpge                       
00001fc1  __aeabi_dcmpgt                       
00001f99  __aeabi_dcmple                       
00001f85  __aeabi_dcmplt                       
0000101d  __aeabi_ddiv                         
00001315  __aeabi_dmul                         
000009a5  __aeabi_dsub                         
00002925  __aeabi_i2d                          
00000b37  __aeabi_idiv0                        
0000315d  __aeabi_memclr                       
0000315d  __aeabi_memclr4                      
0000315d  __aeabi_memclr8                      
0000318d  __aeabi_memcpy                       
0000318d  __aeabi_memcpy4                      
0000318d  __aeabi_memcpy8                      
00002a8d  __aeabi_ui2d                         
00002601  __aeabi_uidiv                        
00002601  __aeabi_uidivmod                     
ffffffff  __binit__                            
00001f09  __cmpdf2                             
0000101d  __divdf3                             
00001f09  __eqdf2                              
000022c5  __fixdfsi                            
00002925  __floatsidf                          
00002a8d  __floatunsidf                        
00001d49  __gedf2                              
00001d49  __gtdf2                              
00001f09  __ledf2                              
00001f09  __ltdf2                              
UNDEFED   __mpu_init                           
00001315  __muldf3                             
00002731  __muldsi3                            
00001f09  __nedf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
000009a5  __subdf3                             
00002a19  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
000031a7  _system_pre_init                     
00003195  abort                                
0000230f  adc_getValue                         
00004970  asc2_0806                            
000044fc  asc2_1206                            
00003f0c  asc2_1608                            
000031b0  asc2_2412                            
ffffffff  binit                                
202006f0  black                                
00001e9d  convertAnalogToDigital               
00002af1  delay_ms                             
20200720  delay_times                          
2020054c  encoderA_cnt                         
20200550  encoderB_cnt                         
20200480  gPWM_0Backup                         
00003169  get_systicks                         
20200554  gpio_interrup1                       
20200558  gpio_interrup2                       
00000000  interruptVectors                     
00000cc1  main                                 
0000183d  normalizeAnalogValues                
20200560  rx_buff                              
00003175  scheduler_init                       
2020055c  task_num                             
20200660  uart_rx_buffer                       
20200724  uart_rx_index                        
20200725  uart_rx_ticks                        
20200700  white                                


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  Way                                  
00000200  __STACK_SIZE                         
00000641  OLED_ShowChar                        
00000811  SYSCFG_DL_GPIO_init                  
000009a5  __aeabi_dsub                         
000009a5  __subdf3                             
000009af  __adddf3                             
000009af  __aeabi_dadd                         
00000b37  __aeabi_idiv0                        
00000b39  No_MCU_Ganv_Sensor_Init              
00000cc1  main                                 
00000df1  GROUP1_IRQHandler                    
00000f11  Set_PWM                              
0000101d  __aeabi_ddiv                         
0000101d  __divdf3                             
00001129  DL_Timer_initFourCCPWMMode           
0000122d  DL_Timer_initTimerMode               
00001315  __aeabi_dmul                         
00001315  __muldf3                             
000013f9  OLED_ShowNum                         
000014db  OLED_Init                            
000015b9  DL_SYSCTL_configSYSPLL               
00001695  TIMG0_IRQHandler                     
0000176d  Get_Analog_value                     
0000183d  normalizeAnalogValues                
000018e7  OLED_ShowSignedNum                   
00001981  OLED_ShowString                      
00001a1d  OLED_DrawPoint                       
00001aad  SYSCFG_DL_PWM_0_init                 
00001b39  SYSCFG_DL_initPower                  
00001bc5  OLED_Refresh                         
00001ccd  __TI_decompress_lzss                 
00001d49  __gedf2                              
00001d49  __gtdf2                              
00001dbd  No_MCU_Ganv_Sensor_Init_Frist        
00001e31  OLED_WR_Byte                         
00001e9d  convertAnalogToDigital               
00001f09  __cmpdf2                             
00001f09  __eqdf2                              
00001f09  __ledf2                              
00001f09  __ltdf2                              
00001f09  __nedf2                              
00001f71  __aeabi_dcmpeq                       
00001f85  __aeabi_dcmplt                       
00001f99  __aeabi_dcmple                       
00001fad  __aeabi_dcmpge                       
00001fc1  __aeabi_dcmpgt                       
00001fd5  OLED_Clear                           
00002035  DL_I2C_fillControllerTXFIFO          
00002095  SYSCFG_DL_I2C_OLED_init              
000020ed  SYSCFG_DL_UART_0_init                
000022c5  __aeabi_d2iz                         
000022c5  __fixdfsi                            
0000230f  adc_getValue                         
00002359  DL_UART_init                         
000023a1  OLED_DisplayTurn                     
000023e9  SYSCFG_DL_ADC12_0_init               
00002431  SYSCFG_DL_SYSCTL_init                
00002479  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
000024bd  No_Mcu_Ganv_Sensor_Task_Without_tick 
00002501  DL_ADC12_setClockConfig              
00002541  Key                                  
00002581  Key_1                                
000025c1  UART0_IRQHandler                     
00002601  __aeabi_uidiv                        
00002601  __aeabi_uidivmod                     
000026b9  Get_Anolog_Value                     
000026f5  __TI_auto_init_nobinit_nopinit       
00002731  __muldsi3                            
000027a1  OLED_ColorTurn                       
000027d5  SYSCFG_DL_TIMER_0_init               
00002809  SYSCFG_DL_init                       
0000286d  OLED_Pow                             
0000289d  SysTick_Handler                      
00002925  __aeabi_i2d                          
00002925  __floatsidf                          
00002a19  _c_int00_noargs                      
00002a67  DL_I2C_setClockConfig                
00002a8d  __aeabi_ui2d                         
00002a8d  __floatunsidf                        
00002af1  delay_ms                             
00002c65  DL_Timer_setCaptCompUpdateMethod     
00002c81  DL_Timer_setClockConfig              
00002eb1  DL_Timer_setCaptureCompareOutCtl     
0000305d  Left_Control                         
00003071  Right_Control                        
00003085  Right_Little_Control                 
000030bd  DL_UART_setClockConfig               
000030cf  TI_memcpy_small                      
000030e1  __TI_decompress_none                 
00003115  DL_Timer_setCaptureCompareValue      
00003125  __TI_zero_init                       
00003135  Get_Digtal_For_User                  
00003143  TI_memset_small                      
00003151  SYSCFG_DL_SYSTICK_init               
0000315d  __aeabi_memclr                       
0000315d  __aeabi_memclr4                      
0000315d  __aeabi_memclr8                      
00003169  get_systicks                         
00003175  scheduler_init                       
00003181  DL_Common_delayCycles                
0000318d  __aeabi_memcpy                       
0000318d  __aeabi_memcpy4                      
0000318d  __aeabi_memcpy8                      
00003195  abort                                
0000319b  ADC0_IRQHandler                      
0000319b  ADC1_IRQHandler                      
0000319b  AES_IRQHandler                       
0000319b  CANFD0_IRQHandler                    
0000319b  DAC0_IRQHandler                      
0000319b  DMA_IRQHandler                       
0000319b  Default_Handler                      
0000319b  GROUP0_IRQHandler                    
0000319b  HardFault_Handler                    
0000319b  I2C0_IRQHandler                      
0000319b  I2C1_IRQHandler                      
0000319b  NMI_Handler                          
0000319b  PendSV_Handler                       
0000319b  RTC_IRQHandler                       
0000319b  SPI0_IRQHandler                      
0000319b  SPI1_IRQHandler                      
0000319b  SVC_Handler                          
0000319b  TIMA0_IRQHandler                     
0000319b  TIMA1_IRQHandler                     
0000319b  TIMG12_IRQHandler                    
0000319b  TIMG6_IRQHandler                     
0000319b  TIMG7_IRQHandler                     
0000319b  TIMG8_IRQHandler                     
0000319b  UART1_IRQHandler                     
0000319b  UART2_IRQHandler                     
0000319b  UART3_IRQHandler                     
0000319e  C$$EXIT                              
0000319f  HOSTexit                             
000031a3  Reset_Handler                        
000031a7  _system_pre_init                     
000031b0  asc2_2412                            
00003f0c  asc2_1608                            
000044fc  asc2_1206                            
00004970  asc2_0806                            
00004c34  __TI_Handler_Table_Base              
00004c40  __TI_Handler_Table_Limit             
00004c48  __TI_CINIT_Base                      
00004c58  __TI_CINIT_Limit                     
00004c58  __TI_CINIT_Warm                      
20200000  OLED_GRAM                            
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
20200480  gPWM_0Backup                         
2020053c  Flag_stop                            
20200540  Flag_stop1                           
20200544  Get_Encoder_countA                   
20200548  Get_Encoder_countB                   
2020054c  encoderA_cnt                         
20200550  encoderB_cnt                         
20200554  gpio_interrup1                       
20200558  gpio_interrup2                       
2020055c  task_num                             
20200560  rx_buff                              
20200660  uart_rx_buffer                       
202006e0  Anolog                               
202006f0  black                                
20200700  white                                
20200718  D_Num                                
2020071c  Run                                  
20200720  delay_times                          
20200724  uart_rx_index                        
20200725  uart_rx_ticks                        
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[189 symbols]
